// src/components/AuthErrorBoundary.tsx
import { ErrorBoundary, FallbackProps } from "react-error-boundary"
import { View, Text, Pressable } from "react-native"
import * as Sentry from "@sentry/react-native"
import { $errorContainer, $errorText, $formButton, $formButtonText } from "@/styles/common"
import { useAppTheme } from "@/utils/useAppTheme"

function AuthErrorFallback({ error, resetErrorBoundary }: FallbackProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed([$errorContainer, { flex: 1, justifyContent: "center", alignItems: "center" }])}>
      <Text style={themed([$errorText, { fontWeight: "bold", fontSize: 18, marginBottom: 10 }])}>
        Authentication Error
      </Text>
      <Text style={themed([$errorText, { marginBottom: 20 }])}>{error.message}</Text>
      <Pressable style={themed($formButton)} onPress={resetErrorBoundary}>
        <Text style={themed($formButtonText)}>Try again</Text>
      </Pressable>
    </View>
  )
}

export function AuthErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      FallbackComponent={AuthErrorFallback}
      onError={(error, info) => {
        Sentry.captureException(error, {
          tags: { component: "authentication" },
          extra: { componentStack: info.componentStack },
        })
      }}
    >
      {children}
    </ErrorBoundary>
  )
}
